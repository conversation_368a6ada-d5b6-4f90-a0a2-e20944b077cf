<?php

namespace App\Http\Controllers\Transact;

use App\Http\Controllers\Controller;
use App\Models\Audit\Audit;
use App\Models\Transaction\MonitorDoc;
use App\Models\Transaction\MonitorDocBl;
use App\Models\Transaction\TMDocHeader;
use App\Models\Transaction\TMDocHeaderBl;
use App\Traits\ImportHelper;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportInvController extends Controller
{
    use ImportHelper;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $blKey = $request->BLKey;
        $invs = DB::table('T_MDOC_bl as T0')
            ->where('BLKey', '=', $blKey)
            ->where('T0.Deleted', '=', 'N')
            ->selectRaw("
                         T0.No_inv
                         ,CONVERT(VARCHAR, T0.Date_inv, 105) AS Date_inv
                         ,T0.Vendor
                         ,T0.Currency
                         ,CAST(T0.Value AS DECIMAL(20, 2)) as Value
                         ,'Details' as Details
                         ,T0.Flags
                         ,T0.DocEntry
                         ,T0.isScan
                         ,T0.isOriginal
                         ,T0.isSend
                         ,T0.FormE
                         ,T0.isFeOri
                         ,T0.FOB
                         ,T0.CostOfRepair
                         ,T0.PackingList
                         ,'Attachments' as Attachments
                         ,T0.SecretKey
                         ,CASE
                             WHEN T0.SecretKey IS NULL THEN (
                                     SELECT COUNT(*) FROM M_Doc_attachment
                                     WHERE M_doc_key = T0.DocEntry AND TransType='Inv'
                                 )
                             ELSE (
                                 SELECT COUNT(*) FROM M_Doc_attachment
                                 WHERE M_doc_key = T0.DocEntry
                                 AND TransType='Inv'
                             )
                         END AS CountAttachment
                         ,T0.MatchKey
                         ,(
                             SELECT COUNT(*) FROM T_MDOC_inv AS a
                             WHERE a.BlKey = ${blKey}
                             AND a.InvKey = T0.DocEntry
                             AND a.Deleted='N'
                             AND A.DocType='Import'
                         ) AS CountInv
                        ,(
                             SELECT COUNT(*) FROM T_MDOC_Container AS a
                             WHERE a.InvId = T0.DocEntry
                         ) AS CountContainer
                         ,(
                            SELECT COUNT(*) FROM T_MDOC_inv AS a
                            WHERE a.BlKey = ${blKey}
                            AND a.InvKey = T0.DocEntry
                            AND a.Deleted='N'
                            AND A.DocType='ReImport'
                        ) AS CountReimport
                         ,T0.DocNum
                     ")
            ->get();

        $openDateTime = str_replace('.', '', microtime(true));
        return response()->json([
            'rows' => $invs,
            'openDateTime' => $openDateTime,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $invoices = $request->invoices;
            $blKey = $request->blKey;
            $insert = 0;
            $update = 0;
            $updateDoc = str_replace('.', '', microtime(true));
            $details = MonitorDoc::where('DocEntry', '=', $blKey)->first();
            $header = TMDocHeader::where('DocEntry', '=', $details->DocNum)->first();
            $testArr = [];
            //            foreach ($invoices as $key => $items) {
            //                $checkDocEntry = $items["DocEntry"];
            //                if (!empty($checkDocEntry)) {
            //                    $openDoc = (float) $request->openDoc;
            //                    $updateDate = DB::table("T_MDOC_bl")
            //                        ->selectRaw("
            //                            CASE
            //                                WHEN UpdateDate IS NULL THEN 0.0
            //                                ELSE CAST(UpdateDate AS FLOAT)
            //                            END AS UpdateDate
            //                        ")
            //                        ->where("DocEntry", "=", $checkDocEntry)
            //                        ->first();
            //
            //                    $updateDocTable = (float) $updateDate->UpdateDate;
            //                    if ($updateDocTable > $openDoc && $updateDocTable < (float) $updateDoc) {
            //                        return response()->json([
            //                            "errors" => true,
            //                            "needReload" => true,
            //                            "message" => "DATA IS NOT SAVED! Please close the modal and open it again!"
            //                        ]);
            //                    }
            //                }
            //            }

            foreach ($invoices as $items) {
                if (empty($items['SecretKey'])) {
                    $generateSecretKey = str_replace('.', '', microtime(true));
                } else {
                    $countSecretKey = DB::table('T_MDOC_bl')
                        ->where('SecretKey', '=', $items['SecretKey'])
                        ->count();

                    if ($countSecretKey > 1) {
                        $generateSecretKey = str_replace('.', '', microtime(true));
                    } else {
                        $generateSecretKey = $items['SecretKey'];
                    }
                }

                $last = $items['DocEntry'] ? $items['DocEntry'] : null;
                $monitorDoc = MonitorDocBl::where('DocEntry', '=', $last)->first();
                $created = !empty($monitorDoc->created_at) ? $monitorDoc->created_at : Carbon::now();
                if ($monitorDoc) {
                    $monitorDoc->No_inv = $items['No_inv'];
                    $monitorDoc->Date_inv = (array_key_exists('Date_inv', $items)) ? $this->toDate($items['Date_inv']) : null;
                    $monitorDoc->Vendor = (array_key_exists('Vendor', $items)) ? $items['Vendor'] : null;
                    $monitorDoc->PackingList = (array_key_exists('PackingList', $items)) ? $items['PackingList'] : $monitorDoc->PackingList;
                    $monitorDoc->Currency = (array_key_exists('Currency', $items)) ? $items['Currency'] : null;
                    $monitorDoc->Value = (array_key_exists('Value', $items)) ? $items['Value'] : null;
                    $monitorDoc->isScan = $items['isScan'] ? $items['isScan'] : 'N';
                    $monitorDoc->isOriginal = $items['isOriginal'] ? $items['isOriginal'] : 'N';
                    $monitorDoc->isSend = $items['isSend'] ? $items['isSend'] : 'N';
                    $monitorDoc->FormE = $items['FormE'] ? $items['FormE'] : 'N';
                    $monitorDoc->isFeOri = $items['isFeOri'] ? $items['isFeOri'] : 'N';
                    $monitorDoc->FOB = (array_key_exists('FOB', $items)) ? $items['FOB'] : 0;
                    $monitorDoc->CostOfRepair = (array_key_exists('CostOfRepair', $items)) ? $items['CostOfRepair'] : 0;
                    $monitorDoc->Updated_by = Auth::user()->username;
                    $monitorDoc->updated_at = Carbon::now();
                    $monitorDoc->created_at = $created;
                    $monitorDoc->Flags = 'U';
                    $monitorDoc->UpdateDate = $updateDoc;
                    $monitorDoc->save();

                    $docEntry = $monitorDoc->DocEntry;

                    $log_inv = Audit::where('auditable_id', '=', $docEntry)
                        ->where('auditable_type', '=', "App\Models\Transaction\MonitorDocBl")
                        ->orderBy('id', 'DESC')
                        ->first();

                    $new_value = json_decode($log_inv->new_values);
                    $old_value = json_decode($log_inv->old_values);

                    if (isset($new_value->No_inv)) {
                        $doc_header = $header->DocNum;
                        if ($old_value->No_inv) {
                            $bl_no = $details->No_bl;
                            $old_inv = $old_value->No_inv;
                            $new_inv = $new_value->No_inv;

                            $old_path = custom_disk_path("docs/IMPORT/${doc_header}/${bl_no}/INV/${old_inv}");
                            $new_path = custom_disk_path("docs/IMPORT/${doc_header}/${bl_no}/INV/${new_inv}");
                            custom_disk_move($old_path, $new_path);
                        }
                    }
                    $update++;
                } else {
                    $monitorDoc = new MonitorDocBl();
                    $monitorDoc->created_at = Carbon::now();
                    $monitorDoc->No_inv = $items['No_inv'];
                    $monitorDoc->Date_inv = (array_key_exists('Date_inv', $items)) ? $this->toDate($items['Date_inv']) : null;
                    $monitorDoc->Vendor = (array_key_exists('Vendor', $items)) ? $items['Vendor'] : null;
                    $monitorDoc->PackingList = (array_key_exists('PackingList', $items)) ? $items['PackingList'] : null;
                    $monitorDoc->Currency = (array_key_exists('Currency', $items)) ? $items['Currency'] : null;
                    $monitorDoc->Value = (array_key_exists('Value', $items)) ? $items['Value'] : null;
                    $monitorDoc->isScan = $items['isScan'] ? $items['isScan'] : 'N';
                    $monitorDoc->isOriginal = $items['isOriginal'] ? $items['isOriginal'] : 'N';
                    $monitorDoc->isSend = $items['isSend'] ? $items['isSend'] : 'N';
                    $monitorDoc->FormE = $items['FormE'] ? $items['FormE'] : 'N';
                    $monitorDoc->isFeOri = $items['isFeOri'] ? $items['isFeOri'] : 'N';
                    $monitorDoc->FOB = (array_key_exists('FOB', $items)) ? $items['FOB'] : 0;
                    $monitorDoc->CostOfRepair = (array_key_exists('CostOfRepair', $items)) ? $items['CostOfRepair'] : 0;
                    $monitorDoc->SecretKey = $generateSecretKey;
                    //$monitorDoc->SecretKey = $items["SecretKey"];
                    $monitorDoc->MatchKey = $items['MatchKey'];
                    $monitorDoc->Created_by = Auth::user()->username;
                    $monitorDoc->BLKey = $blKey;
                    $monitorDoc->Flags = 'I';
                    $monitorDoc->UpdateDate = $updateDoc;
                    $monitorDoc->save();

                    $docEntry = $monitorDoc->DocEntry;
                    $insert++;
                }
                $this->updateAttachmentMDocKey($items['SecretKey'], $docEntry, 'Inv');
            }
            $countInv = DB::table('T_MDOC_bl')
                ->where('BlKey', '=', $blKey)
                ->where('Deleted', '=', 'N')
                ->count();
            return response()->json([
                'total' => $countInv,
                'message' => "Insert => ${insert}, Update => ${update}",
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Upload Excel to update ContainerNo in T_MDOC
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadExcel(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls'
        ]);

        $file = $request->file('file');
        $spreadsheet = IOFactory::load($file->getPathname());
        $sheet = $spreadsheet->getActiveSheet();
        $rows = $sheet->toArray();

        if (count($rows) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Excel file is empty or missing data rows.'
            ], 422);
        }

        // Flexible column matching
        $header = array_map(function ($h) {
            return strtolower(trim($h));
        }, $rows[0]);

        $findColumn = function ($header, $candidates) {
            foreach ($header as $idx => $col) {
                $colNorm = strtolower(preg_replace('/[^a-z0-9]/', '', $col));
                foreach ($candidates as $candidate) {
                    $candNorm = strtolower(preg_replace('/[^a-z0-9]/', '', $candidate));
                    if (strpos($colNorm, $candNorm) !== false) {
                        return $idx;
                    }
                }
            }
            return false;
        };

        $containerNoIdx = $findColumn($header, ['container no']);
        $blNoIdx = $findColumn($header, ['bl no']);
        $noInvoiceIdx = $findColumn($header, ['invoice no']);

        if ($containerNoIdx === false || $blNoIdx === false || $noInvoiceIdx === false) {
            return response()->json([
                'success' => false,
                'message' => 'Excel must have columns similar to: Container No, BL No, No Invoice.'
            ], 422);
        }

        $updated = 0;
        $notFound = [];
        $updatedDocEntries = [];
        for ($i = 1; $i < count($rows); $i++) {
            $row = $rows[$i];
            $containerNo = $row[$containerNoIdx];
            $blNo = $row[$blNoIdx];
            $noInvoice = $row[$noInvoiceIdx];

            if (!$containerNo || !$blNo || !$noInvoice) {
                $notFound[] = [
                    'row' => $i + 1,
                    'reason' => 'Missing required value(s)'
                ];
                continue;
            }

            $doc = DB::table('T_MDOC')
                ->join('T_MDOC_Bl', function ($join) use ($noInvoice) {
                    $join->on('T_MDOC.DocEntry', '=', 'T_MDOC_Bl.BlKey')
                        ->where('T_MDOC_Bl.Deleted', '=', 'n')
                        ->where('T_MDOC_Bl.No_inv', '=', $noInvoice);
                })
                ->where('T_MDOC.No_bl', $blNo)
                ->select(["T_MDOC_Bl.DocEntry"])
                ->first();

            if ($doc) {
                $container = DB::table('T_MDOC_Container')
                    ->where('InvId', $doc->DocEntry)
                    ->where('ContainerNo', $containerNo)
                    ->first();
                if ($container) {
                    $container->update([
                        'ContainerSize' => $containerNo,
                        'Remark' => $containerNo,
                    ]);
                } else {
                    DB::table('T_MDOC_Container')->insert([
                        'InvId' => $doc->DocEntry,
                        'ContainerNo' => $containerNo,
                        'ContainerSize' => $containerNo,
                        'Remark' => $containerNo,
                        'CreatedBy' => Auth::id() ?? 0,
                    ]);
                }
                DB::table('T_MDOC_Container')
                    ->where('InvId', $doc->DocEntry)
                    ->where('ContainerNo', $containerNo)
                    ->update([
                        // 'ContainerNo' => $containerNo,
                        'ContainerSize' => $containerNo,
                        'Remark' => $containerNo,
                    ]);
                $updated++;
                $updatedDocEntries[] = $doc->DocEntry;
            } else {
                $notFound[] = [
                    'row' => $i + 1,
                    'bl_no' => $blNo,
                    'no_invoice' => $noInvoice,
                    'reason' => 'No matching T_MDOC record found'
                ];
            }
        }

        // Fetch affected data with joins and required fields
        $effectedData = [];
        if (!empty($updatedDocEntries)) {
            $effectedData = DB::table('T_MDOC')
                ->join('T_MDOC_Header', 'T_MDOC.DocNum', '=', 'T_MDOC_Header.DocEntry')
                ->join('M_Tenant', 'T_MDOC.Tenant_key', '=', 'M_Tenant.DocEntry')
                ->where('T_MDOC.DocType', 'Import')
                ->whereIn('T_MDOC.DocEntry', $updatedDocEntries)
                ->select([
                    'T_MDOC_Header.DocNum as header_docnum',
                    'T_MDOC_Header.Cargo',
                    'T_MDOC_Header.Voyage',
                    'T_MDOC_Header.Shipement',
                    'T_MDOC.No_bl',
                    'T_MDOC.date_bl',
                    'M_Tenant.Name as tenant_name',
                ])
                ->get();
        }

        return response()->json([
            'success' => true,
            'updated' => $updated,
            'not_found' => $notFound,
            'effected_data' => $effectedData,
            'message' => "Updated $updated records. " . (count($notFound) ? count($notFound) . ' rows not matched.' : '')
        ]);
    }

    /**
     * @param $request
     *
     * @return string|null
     */
    public function toDate($request)
    {
        if (!empty($request)) {
            $date = strtotime($request);
            return date('Y-m-d', $date);
        }
        return null;


    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            if ($this->validation($request)) {
                return response()->json([
                    'errors' => true,
                    'validHeader' => true,
                    'message' => $this->validation($request),
                ]);
            }
            $form = $request->form;
            $details = collect($request->details);
            $type = $request->type;
            $docNum = null;
            // Update header
            if ($type == 'all') {
                $header = TMDocHeaderBl::where('DocEntry', '=', $id)->first();
                $created = $header->created_at;
                $header->No_bl = $request->form['No_bl'];
                $header->Date_bl = $request->form['Date_bl'];
                $header->PIB_no = $request->form['PIB_no'];
                $header->PIB_date = $request->form['PIB_date'];
                $header->updated_at = Carbon::now();
                $header->created_at = $created;
                $header->Updated_by = Auth::user()->username;
                $header->Flags = 'J';
                $header->save();
                $docNum = $header->DocEntry;
            }

            foreach ($details as $i => $items) {
                $docEntry = $this->saveData($i, $items, $id);
            }

            if ($type == 'row') {
                $countInvHeader = DB::table('T_MDOC_Header_inv')->where('DocNum', '=', $id)->count();
                return response()->json([
                    'errors' => false,
                    'docEntry' => $id,
                    'status' => $countInvHeader > 0 ? 'update' : 'add',
                    'message' => 'Data updated!',
                ]);
            }
            return response()->json([
                'errors' => false,
                'message' => 'Data updated!',
            ]);


        } catch (\Exception $e) {
            return response()->json(
                [
                    'errors' => true,
                    'message' => $e->getMessage(),
                ]
            );
        }
    }

    /**
     * @param $request
     *
     * @return bool|\Illuminate\Support\MessageBag
     */
    public function validation($request)
    {
        $messages = [];
        $messages = [
            'form.No_bl.required' => 'B/L no is required!',
            'form.Date_bl.required' => 'B/L date is required!',
            'form.PIB_no.required' => 'PIB no is required!',
            'form.PIB_date.required' => 'PIB date is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.No_bl' => 'required',
            'form.Date_bl' => 'required',
            'form.PIB_no' => 'required',
            'form.PIB_date' => 'required',
        ], $messages);

        if ($validator->fails()) {
            return $validator->errors();
        }
        return false;


    }

    /**
     * @param $i
     * @param $items
     * @param $id
     *
     * @return int|mixed
     */
    public function saveData($i, $items, $id)
    {
        $last = $items[5];

        $index = $i;
        $index *= 0;
        $monitorDoc = MonitorDocBl::where('DocEntry', '=', $last)->first();
        $created = !empty($monitorDoc->created_at) ? $monitorDoc->created_at : Carbon::now();
        if ($monitorDoc) {
            $monitorDoc->No_inv = $items[$index];
            $monitorDoc->Date_inv = $this->toDate($items[++$index]);
            $monitorDoc->Vendor = $items[++$index];
            $monitorDoc->Value = $items[++$index];
            $monitorDoc->Updated_by = Auth::user()->username;
            $monitorDoc->updated_at = Carbon::now();
            $monitorDoc->created_at = $created;
            $monitorDoc->Flags = 'U';
            $monitorDoc->save();
            $docEntry = $monitorDoc->DocEntry;
        } else {
            $monitorDoc = new MonitorDocBl();
            $monitorDoc->created_at = Carbon::now();
            $monitorDoc->No_inv = $items[$index];
            $monitorDoc->Date_inv = $this->toDate($items[++$index]);
            $monitorDoc->Vendor = $items[++$index];
            $monitorDoc->Value = $items[++$index];
            $monitorDoc->Created_by = Auth::user()->username;
            $monitorDoc->DocNum = $id;
            $monitorDoc->Flags = 'I';
            $monitorDoc->save();
            $docEntry = $monitorDoc->DocEntry;
        }
        return $docEntry;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

    }
}
