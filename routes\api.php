<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\OAuthClientController;
use App\Http\Controllers\Api\OAuthTokenController;
use App\Http\Controllers\Api\StorageController;
use App\Http\Controllers\Approval\BatchApprovalController;
use App\Http\Controllers\Approval\DownloadBatchController;
use App\Http\Controllers\Approval\SyncSerialNumberController;
use App\Http\Controllers\Audit\AuditController;
use App\Http\Controllers\Billing\AssistTugController;
use App\Http\Controllers\Billing\BillingExportController;
use App\Http\Controllers\Billing\BillingImportController;
use App\Http\Controllers\Billing\BillingLocalController;
use App\Http\Controllers\Billing\TimeSheetController;
use App\Http\Controllers\BoundedZone\BoundedZoneController;
use App\Http\Controllers\Catalog\CatalogController;
use App\Http\Controllers\Catalog\CatalogDetailController;
use App\Http\Controllers\Catalog\CatalogImgController;
use App\Http\Controllers\Common\AttachmentCommonController;
use App\Http\Controllers\Config\ItemMasterSapController;
use App\Http\Controllers\Dashboard\BcTypeDashboardController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Dashboard\StatsController;
use App\Http\Controllers\Document\CancelDocumentController;
use App\Http\Controllers\Document\CheckStatusBatchPrivyController;
use App\Http\Controllers\Document\DocumentPrivyController;
use App\Http\Controllers\Employee\DepartmentController;
use App\Http\Controllers\Employee\EmployeeController;
use App\Http\Controllers\Employee\WorkShiftController;
use App\Http\Controllers\Export\ApprovalCheckListController;
use App\Http\Controllers\Export\DownloadDraftAttachmentController;
use App\Http\Controllers\Export\DraftController;
use App\Http\Controllers\Export\KbReportExport;
use App\Http\Controllers\Export\SubmitDraftSignController;
use App\Http\Controllers\Export\TradingExportController;
use App\Http\Controllers\Helper\TransactHelperController;
use App\Http\Controllers\Local\AttachmentController;
use App\Http\Controllers\Local\LocalController;
use App\Http\Controllers\Local\MainLocalController;
use App\Http\Controllers\Mail\SendEmailController;
use App\Http\Controllers\Master\AgentController;
use App\Http\Controllers\Master\BillingItemController;
use App\Http\Controllers\Master\BusinessPartnerController;
use App\Http\Controllers\Master\CargoController;
use App\Http\Controllers\Master\CategoryItemController;
use App\Http\Controllers\Master\ClassificationController;
use App\Http\Controllers\Master\CurrencyController;
use App\Http\Controllers\Master\DestinationPortController;
use App\Http\Controllers\Master\DisplayFormController;
use App\Http\Controllers\Master\ExchangeRateController;
use App\Http\Controllers\Master\JettyController;
use App\Http\Controllers\Master\LocalItemController;
use App\Http\Controllers\Master\MappingTableController;
use App\Http\Controllers\Master\MasterCategoryClassification;
use App\Http\Controllers\Master\MasterCountryController;
use App\Http\Controllers\Master\MasterPermissionController;
use App\Http\Controllers\Master\MasterPPJKController;
use App\Http\Controllers\Master\MasterRolesController;
use App\Http\Controllers\Master\MasterSignerController;
use App\Http\Controllers\Master\MasterTradingController;
use App\Http\Controllers\Master\PortOfLoadingController;
use App\Http\Controllers\Master\PriceListAssisTugController;
use App\Http\Controllers\Master\PriceListPortSeviceControler;
use App\Http\Controllers\Master\PriceListServiceLoadingControler;
use App\Http\Controllers\Master\PriceListTenantControler;
use App\Http\Controllers\Master\RemarkController;
use App\Http\Controllers\Master\RolesController;
use App\Http\Controllers\Master\SignatureController;
use App\Http\Controllers\Master\SurveyorController;
use App\Http\Controllers\Master\TenantController;
use App\Http\Controllers\Master\TenantLetterController;
use App\Http\Controllers\Master\TipeBcController;
use App\Http\Controllers\Master\TongkangController;
use App\Http\Controllers\Master\TransTypeController;
use App\Http\Controllers\Master\TugBoatController;
use App\Http\Controllers\Master\UserController;
use App\Http\Controllers\Master\UserEmailController;
use App\Http\Controllers\Master\UserRoleController;
use App\Http\Controllers\Report\ReportListController;
use App\Http\Controllers\Settings\SettingController;
use App\Http\Controllers\TExport\ExportController;
use App\Http\Controllers\Trading\AttachmentController as AttachmentTradingController;
use App\Http\Controllers\Trading\MainTradingController;
use App\Http\Controllers\Trading\sendMailTrading;
use App\Http\Controllers\Trading\TradingController;
use App\Http\Controllers\Transact\DetailsImportController;
use App\Http\Controllers\Transact\ImportController;
use App\Http\Controllers\Transact\ImportDataController;
use App\Http\Controllers\Transact\ImportInvController;
use App\Http\Controllers\Transact\ImportInvDetailController;
use App\Http\Controllers\Transact\ImportInvDetailSubController;
use App\Http\Controllers\Transact\ImportLogsController;
use App\Http\Controllers\Transact\ImportViewController;
use App\Http\Controllers\Transact\JettyImportController;
use App\Http\Controllers\Transact\MonitorDocumentController;
use App\Http\Controllers\Transact\NotulController;
use App\Http\Controllers\Transact\PelabuhanImportController;
use App\Http\Controllers\Transact\TMDOCHeaderLocalController;
use App\Http\Controllers\Transact\ViewTransactionController;
use App\Http\Controllers\Transaction\DocConfirmProcessController;
use App\Http\Controllers\Transaction\PostingPeriodController;
use App\Http\Controllers\Transaction\RevisionTransactionController;
use App\Services\BiCurrencyService;
use Illuminate\Support\Facades\Route;
use Modules\Master\App\Http\Controllers\HsCodeTypeController;
use Modules\Master\App\Http\Controllers\MasterHsCodeController;
use Modules\Master\App\Http\Controllers\SettingListController;
use Modules\PortTransaction\App\Http\Controllers\DocQtyInvController;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use App\Http\Controllers\Transact\ImportInvContainerController;

Route::post('/login', [AuthController::class, 'login']);
Route::post('/login-auth-service', [AuthController::class, 'loginAuthService']);

Route::group(['middleware' => ['auth_service']], function () {
    Route::get('doc-confirm-process', [DocConfirmProcessController::class, 'index']);
});

Route::get('bi-kurs', function (Request $request) {
    $curl = curl_init();
    $currency = $request->currency ?? 'USD';
    $startDate = $request->startDate ?? now()->addDays(-7)->format("Ymd");
    $endDate = $request->endDate ?? now()->format("Ymd");
    $biCurrencyService = new BiCurrencyService();
    $exchangeRates = $biCurrencyService->getRate($currency, $startDate, $endDate);
    return response()->json([
        'success' => true,
        'message' => 'Exchange rates retrieved successfully',
        'data' => $exchangeRates
    ]);
});


Route::middleware(['auth_service'])
    ->prefix('v1/dashboard/')
    ->name('api.dashboard.')
    ->group(function () {
        Route::get('bc-type', [BcTypeDashboardController::class, 'index']);
    });



Route::group(['middleware' => ['auth_service']], function () {
    Route::get('stats', [StatsController::class, 'index']);
    Route::apiResource('attachment', AttachmentCommonController::class);

    Route::group(["prefix" => "config"], function () {
        Route::apiResources([
            'item-sap' => ItemMasterSapController::class,
            'hs-code' => MasterHsCodeController::class
        ]);
    });

    Route::group(["prefix" => "auth"], function () {
        Route::post('user', [AuthController::class, 'show']);
        Route::post('/roles', [AuthController::class, 'roles']);
        Route::post('/permissions', [AuthController::class, 'permissions']);
        Route::post('/logout', [AuthController::class, 'logout']);
    });

    Route::group(["prefix" => "oauth"], function () {
        // Client management routes
        Route::get('/clients', [OAuthClientController::class, 'index']);
        Route::post('/clients', [OAuthClientController::class, 'store']);
        Route::put('/clients/{client_id}', [OAuthClientController::class, 'update']);
        Route::delete('/clients/{client_id}', [OAuthClientController::class, 'destroy']);

        // Token management routes
        Route::get('/tokens', [OAuthTokenController::class, 'index']);
        Route::delete('/tokens/{token_id}', [OAuthTokenController::class, 'destroy']);
        Route::delete('/tokens', [OAuthTokenController::class, 'revokeAll']);
    });

    Route::group(["prefix" => "setting"], function () {
        Route::get('list', [SettingListController::class, 'index']);
        Route::get('hs-code-type', [HsCodeTypeController::class, 'getForSelect']);
        Route::apiResource('hs-code', HsCodeTypeController::class)->names([
            'index' => 'setting.hs-code.index',
            'store' => 'setting.hs-code.store',
            'show' => 'setting.hs-code.show',
            'update' => 'setting.hs-code.update',
            'destroy' => 'setting.hs-code.destroy'
        ]);
    });
    Route::apiResource('settings', SettingController::class);

    Route::group(["prefix" => "storage"], function () {
        Route::post('{storage}/list', [StorageController::class, 'show']);
        Route::post('{storage}/upload', [StorageController::class, 'upload']);
        Route::post('{storage}/mkdir', [StorageController::class, 'mkdir']);
        Route::post('{storage}/delete', [StorageController::class, 'destroy']);
        Route::post('{storage}/rename', [StorageController::class, 'rename']);
    });

    Route::get('check-kb', [BoundedZoneController::class, 'check']);

    Route::post('menu', [AuthController::class, 'menuUser']);
    Route::post('menus', [DashboardController::class, 'menu']);
    Route::post('union-bp', [DashboardController::class, 'unioBp']);

    Route::post("download", [DraftController::class, 'downloadFile']);


    Route::post('user-role', [UserRoleController::class, 'getUserRole']);
    Route::post('form-cancel/submit', [CancelDocumentController::class, 'submitSign']);
    Route::get('form-cancel/download', [CancelDocumentController::class, 'downloadSign']);

    Route::apiResources([
        'form-cancel' => CancelDocumentController::class
    ]);

    /**
     * Billing Route
     */
    Route::group(["prefix" => "billing"], function () {
        Route::post("vessel-status", [BillingExportController::class, "vesselStatus"]);
        Route::post("export-params", [BillingImportController::class, "exportParams"]);
        Route::post('signature', [BillingImportController::class, 'signatureList']);
        Route::post('time-sheet-index', [TimeSheetController::class, 'index']);
        Route::apiResource('time-sheet', TimeSheetController::class);

        Route::post('assist-tug-index', [AssistTugController::class, 'index']);
        Route::post('assist-tug', [AssistTugController::class, 'store']);
        Route::post('assist-tug-export', [AssistTugController::class, 'export']);
        Route::post('group-data', [BillingImportController::class, "groupData"]);
        Route::post('email-document', [BillingImportController::class, 'emailDocument']);

        Route::group(["prefix" => "export"], function () {
            Route::post("fetch-docnum", [BillingExportController::class, "fetchDocNum"]);
            Route::post('cargo', [BillingExportController::class, 'dataCargo']);
            Route::post('export-details', [BillingExportController::class, 'exportDetails']);
            Route::post('list', [BillingExportController::class, 'index']);
            Route::post('report', [BillingExportController::class, 'reportBilling']);
            Route::post('get-export', [BillingExportController::class, 'getExportData']);
            Route::post('get-partial', [BillingExportController::class, 'getPartial']);
            Route::post('index/{id}', [BillingExportController::class, 'dataBilling']);
            Route::post('loading-unloading-type', [BillingImportController::class, 'loadingUnLoading']);
            Route::post('port-service', [BillingExportController::class, 'getPortService']);
            Route::post('voyage', [BillingExportController::class, 'voyageBYVessel']);

            Route::post('export-details-store', [BillingExportController::class, 'storeExportDetails']);
            Route::post('cancel/{id}', [BillingExportController::class, 'cancelDoc']);
            Route::post('remove-detail', [BillingExportController::class, 'removeExportDetails']);
            Route::post('store/{id}', [BillingExportController::class, 'store']);
        });

        Route::group(["prefix" => "import"], function () {
            Route::post("attachment", [BillingImportController::class, "getDocAttachment"]);
            Route::post("fetch-docnum", [BillingImportController::class, "fetchDocNum"]);
            Route::post('cargo', [BillingImportController::class, 'dataCargo']);
            Route::post('list', [BillingImportController::class, 'index']);
            Route::post('import-details', [BillingImportController::class, 'exportDetails']);
            Route::post('report', [BillingImportController::class, 'reportBilling']);
            Route::post('get-export', [BillingImportController::class, 'getExportData']);
            Route::post('get-partial', [BillingImportController::class, 'getPartial']);
            Route::post('index/{id}', [BillingImportController::class, 'dataBilling']);
            Route::post('loading-unloading-type', [BillingImportController::class, 'loadingUnLoading']);
            Route::post('port-service', [BillingImportController::class, 'getPortService']);
            Route::post('voyage', [BillingImportController::class, 'voyageBYVessel']);
            Route::post('weight-category', [BillingImportController::class, 'weightCategory']);

            Route::post('import-details-store', [BillingImportController::class, 'storeExportDetails']);
            Route::post('cancel/{id}', [BillingImportController::class, 'cancelDoc']);
            Route::post('remove-attachment/{id}', [BillingImportController::class, 'deleteMedia']);
            Route::post('remove-detail', [BillingImportController::class, 'removeExportDetails']);
            Route::post('attachment-store', [BillingImportController::class, 'storeMedia']);
            Route::post('store/{id}', [BillingImportController::class, 'store']);
        });

        Route::group(["prefix" => "local"], function () {
            Route::post("fetch-docnum", [BillingLocalController::class, "fetchDocNum"]);
            Route::post('cargo', [BillingLocalController::class, 'dataCargo']);
            Route::post('list', [BillingLocalController::class, 'index']);
            Route::post('local-details', [BillingLocalController::class, 'exportDetails']);
            Route::post('report', [BillingLocalController::class, 'reportBilling']);
            Route::post('get-export', [BillingLocalController::class, 'getExportData']);
            Route::post('get-partial', [BillingLocalController::class, 'getPartial']);
            Route::post('index/{id}', [BillingLocalController::class, 'dataBilling']);
            Route::post('loading-unloading-type', [BillingImportController::class, 'loadingUnLoading']);
            Route::post('port-service', [BillingLocalController::class, 'getPortService']);
            Route::post('voyage', [BillingLocalController::class, 'voyageBYVessel']);
            Route::post('calculate-estimate', [BillingLocalController::class, 'calculateEstimate']);

            Route::post('local-details-store', [BillingLocalController::class, 'storeExportDetails']);
            Route::post('cancel/{id}', [BillingLocalController::class, 'cancelDoc']);
            Route::post('remove-detail', [BillingLocalController::class, 'removeExportDetails']);
            Route::post('store/{id}', [BillingLocalController::class, 'store']);
        });
    });

    Route::group(["prefix" => "local"], function () {
        // local
        Route::patch("save/{id}", [LocalController::class, 'update']);
        Route::delete("destroy/{id}", [LocalController::class, 'destroy']);
        Route::post("TLocal-index", [MainLocalController::class, 'TLocal']);
        Route::post("TLocal", [MainLocalController::class, 'TLocal']);

        Route::post("max-doc-local", [MainLocalController::class, 'MaxLocalNumber']);
        Route::post("local-detail/{id}", [LocalController::class, 'localDetails']);
        Route::post('cargo', [LocalController::class, 'cargo']);
        Route::post("fetch-docnum-local", [MainLocalController::class, "fetchDocNum"]);
        Route::post('attachment', [AttachmentController::class, "storeMedia"]);
        Route::post('/attachment/{id}', [AttachmentController::class, "listMedia"]);
        Route::POST('deleteFIle/{id}', [AttachmentController::class, "delFile"]);
    });

    Route::group(["prefix" => "trading"], function () {
        // trading
        Route::post("BP", [TradingController::class, 'loadBP']);
        Route::post("Username", [TradingController::class, 'getUsername']);
        Route::post("import-details/{id}", [TradingController::class, 'importDetails']);
        Route::post("invoice-details/{id}", [TradingController::class, 'invoiceDetails']);
        Route::post("show-Invoice/{id}", [TradingController::class, 'showInvoice']);
        Route::post("item-trading/{id}", [TradingController::class, 'itemTrading']);
        Route::post("item-Invoice/{id}", [TradingController::class, 'itemInvoice']);
        Route::post('/attachment/{id}', [AttachmentTradingController::class, "listMedia"]);
        Route::post('getUserAccess/{id}', [MainTradingController::class, "listUserAccess"]);
        Route::post('getTenant', [MainTradingController::class, "listTenant"]);
        Route::post('getBC', [MainTradingController::class, "listBC"]);
        Route::post('getBP', [MainTradingController::class, "listBP"]);
        Route::post('mainData', [MainTradingController::class, "getData"]);
        Route::post('mainUpdateDel/{id}', [mainTradingController::class, "updateDel"]);

        Route::post('UpdateStatus', [TradingController::class, "UpdateStatus"]);
        Route::post('DeleteRow', [TradingController::class, "DeleteRow"]);
        Route::post('DeleteInv', [TradingController::class, "DeleteInv"]);
        Route::post('DeleteItem', [TradingController::class, "DeleteItem"]);
        Route::post('reOpen', [TradingController::class, "reOpen"]);
        Route::post('ViewLog', [TradingController::class, "ViewLog"]);
        Route::post('getDatax', [MainTradingController::class, "getDatax"]);

        Route::post('mainSave', [MainTradingController::class, "store"]);
        Route::post('mainUpdate', [MainTradingController::class, "updateData"]);
        Route::post('saveInvoice', [TradingController::class, "saveInvoice"]);
        Route::post('saveInvoiceDetail', [TradingController::class, "saveInvoiceDetail"]);
        Route::post('saveTradingItem', [TradingController::class, "saveTradingItem"]);
        Route::post("Detail", [tradingController::class, 'storeDetail']);
        Route::post('attachment', [AttachmentTradingController::class, "storeMedia"]);
        Route::post('approval-trading', [AttachmentTradingController::class, "approvalAction"]);
        Route::post('deleteFIle/{id}', [TradingController::class, "delFile"]);
        Route::post('sendMail', [sendMailTrading::class, "sendIt"]);
        Route::post('getMailData', [sendMailTrading::class, "getMailData"]);
        Route::post('downloadAttachment', [TradingExportController::class, "zipAttachment"]);
        Route::post('getServerTime', [TradingController::class, "getServerTime"]);
        Route::post('exportItemTrading', [TradingExportController::class, "tradingItemExport"]);
        Route::post('updateInvType', [TradingController::class, "updateInvType"]);
    });

    Route::group(["prefix" => "transaction"], function () {
        Route::get('vessel-list', [RevisionTransactionController::class, 'getVessel']);
        Route::get('header', [RevisionTransactionController::class, 'getHeader']);
        Route::get('header/{id}', [RevisionTransactionController::class, 'getSingleHeader']);
        Route::get('detail', [RevisionTransactionController::class, 'getDetail']);
        Route::get('print-approval', [ApprovalCheckListController::class, 'print']);
        Route::apiResources([
            'revision-qty' => RevisionTransactionController::class,
            'posting-period' => PostingPeriodController::class,
            'doc-qty-inv' => DocQtyInvController::class,
        ]);
    });

    Route::group(["prefix" => "transact"], function () {

        Route::get('check-privy-status', [DocumentPrivyController::class, 'show']);
        Route::post('batch-approval', [BatchApprovalController::class, 'index']);
        Route::post('download-batch', [DownloadBatchController::class, 'store']);
        Route::post('re-process-batch', [BatchApprovalController::class, 'reProcessBatch']);
        Route::get('status-privy/{id}', [CheckStatusBatchPrivyController::class, 'show']);
        Route::post('sync-serial-number ', [SyncSerialNumberController::class, 'store']);

        /**
         * Transaction Route
         */
        Route::get('vessel-import', [ImportDataController::class, 'vessel']);
        Route::get('shipment-by-vessel', [ImportDataController::class, 'shipmentByVessel']);
        Route::get('import-data', [ImportDataController::class, 'importData']);
        Route::post('import-data-2', [ImportDataController::class, 'importData2']);
        Route::post('cb', [TransactHelperController::class, 'updateBlField']);
        Route::post("fetch-docnum", [ImportController::class, "fetchDocNum"]);
        Route::post("fetch-docnum-export", [ExportController::class, "fetchDocNum"]);
        Route::post("fetch-trading-docnum", [TMDOCHeaderLocalController::class, "fetchDocNum"]);
        Route::post("itemcode", [ImportController::class, "loadItem"]);
        Route::post('import-doc', [ImportController::class, 'importDoc']);
        Route::post('import-type', [ImportController::class, 'importType']);

        Route::post('export/attachment/{id}', [ExportController::class, "getDocAttachment"]);
        Route::post('import/e-sign', [ImportController::class, "getESign"]);
        Route::post("export-details/{id}", [ExportController::class, "exportDetails"]);
        // Route::post('import/attachment/{id}', [ImportController::class, "getDocAttachment"]);
        Route::post('import/sppb-status', [ImportController::class, "sppbStatus"]);
        Route::post("import-details/{id}", [ImportController::class, "importDetails"]);
        Route::post("pelabuhan-import-details/{id}", [PelabuhanImportController::class, "importDetails"]);
        Route::post("import-details-view/{id}", [ImportViewController::class, "importDetails"]);
        Route::get('monitor/attachment/{id}', [MonitorDocumentController::class, "getDocAttachment"]);
        Route::post('monitor/attachment/{id}', [MonitorDocumentController::class, "getDocAttachment"]);

        Route::post('max-doc-import', [ImportController::class, 'maxDocImport']);
        Route::post('max-doc-export', [ExportController::class, 'maxDocExport']);
        Route::post('max-doc-trading', [TMDOCHeaderLocalController::class, 'maxDocImport']);

        Route::post('monitor-import', [ViewTransactionController::class, 'monitorImport']);



        Route::post("process-logs", [ImportLogsController::class, "getProcessLogs"]);
        Route::post('row-data', [ImportController::class, 'getRowData']);
        Route::post('tipebc/{id}', [TipeBcController::class, 'getBcByTrans']);
        Route::post("remark-header/{id}", [ImportController::class, "getRemarkHeader"]);

        //Route::put('import/{DocEntry}/{DocNum}', [ImportController::class, 'updateData']);

        Route::post("checkInv", [ImportController::class, "checkInv"]);
        // Route::post('import/attachment', [ImportController::class, "storeMedia"]);
        Route::post('export/attachment', [ExportController::class, "storeMedia"]);
        // Route::post('download-attachment', [ImportController::class, "downloadAttachment"]);

        Route::post('export/remove-attachment/{id}', [ExportController::class, "deleteMedia"]);
        Route::post('import/remove-attachment/{id}', [MonitorDocumentController::class, "deleteMedia"]);
        Route::post('monitor/remove-attachment/{id}', [MonitorDocumentController::class, "deleteMedia"]);
        Route::post('monitor/attachment/{id}', [MonitorDocumentController::class, "storeMedia"]);
        Route::post('monitor/delete/attachment/media', [MonitorDocumentController::class, "deleteMedia"]);

        Route::post("re-open/{docentry}", [ImportController::class, "reOpenDocStatus"]);
        Route::post("close-document/{docentry}", [ImportController::class, "closeDocument"]);
        Route::post("remove-doc", [ImportController::class, "removeDoc"]);
        Route::post("sync", [MonitorDocumentController::class, "sync"]);
        Route::post("validate-process-export", [ExportController::class, "checkInv"]);
        Route::post("restore-import-header/{id}", [ImportController::class, "restoreHeader"]);
        Route::post("delete-import-header/{id}", [ImportController::class, "deleteHeader"]);
        Route::post("pelabuhan-import/update-data", [PelabuhanImportController::class, "store"]);

        // Route::post('import-doc-form', [ImportController::class, 'importDocForm']);
        Route::post('detail-import-index', [DetailsImportController::class, 'index']);
        Route::apiResource('detail-import', DetailsImportController::class);
        Route::post('import-index', [ImportController::class, 'index']);
        Route::post('import-delete/{id}', [ImportController::class, 'destroy']);
        Route::post('trading-delete/{id}', [TradingController::class, 'destroy']);
        Route::apiResource('import', ImportController::class);

        Route::post('pelabuhan-import-index', [PelabuhanImportController::class, 'index']);
        Route::apiResource('pelabuhan-import', PelabuhanImportController::class);

        Route::post('export-index', [ExportController::class, 'index']);
        Route::post('export-destroy/{id}', [ExportController::class, 'destroy']);
        Route::apiResource('export', ExportController::class);
        Route::post('import-inv-index', [ImportInvController::class, 'index']);
        Route::apiResource('import-inv', ImportInvController::class);
        Route::apiResource('import-inv-container', ImportInvContainerController::class);
        Route::post('import-inv-detail-index', [ImportInvDetailController::class, 'index']);
        Route::apiResource('import-inv-detail', ImportInvDetailController::class);
        Route::post('import-inv-detail-sub-index', [ImportInvDetailSubController::class, 'index']);
        Route::apiResource('import-inv-detail-sub', ImportInvDetailSubController::class);
        Route::post('import-inv/upload-excel', [ImportInvController::class, 'uploadExcel']);
        Route::post('monitor-doc-index', [MonitorDocumentController::class, 'index']);
        Route::apiResource('monitor-doc', MonitorDocumentController::class);
        Route::post('trading-index', [TMDOCHeaderLocalController::class, 'index']);

        Route::apiResource('trading', TMDOCHeaderLocalController::class)->names([
            'index' => 'transact.trading.index',
            'store' => 'transact.trading.store',
            'show' => 'transact.trading.show',
            'update' => 'transact.trading.update',
            'destroy' => 'transact.trading.destroy'
        ]);

        Route::apiResource('notul', NotulController::class);
        Route::apiResource('audit', AuditController::class);
        Route::delete('audit-header/{id}', [AuditController::class, 'deleteHeader']);
        Route::post('audit-attachment/{id}', [AuditController::class, 'show']);
        Route::post('audit-export', [AuditController::class, 'export']);
    });

    Route::group(['prefix' => 'report-list'], function () {
        Route::get('/params', [ReportListController::class, 'params']);
        Route::get('/preview', [ReportListController::class, 'show']);
        Route::get('/export', [ReportListController::class, 'export']);
    });

    Route::group(["prefix" => "export"], function () {
        Route::post('bounded-zone', [KbReportExport::class, 'exportKbReport']);
        Route::post('draft', [DraftController::class, 'exportDraft']);
        //Route::post('draft-attachment', [DraftController::class, 'exportDraftAttachment']);
        Route::post('draft-attachment', [DownloadDraftAttachmentController::class, 'store']);
        Route::post('lpb', [JettyImportController::class, 'exportDraft']);
        //Route::post('approval', [DraftController::class, 'approval']);
        Route::post('approval', [SubmitDraftSignController::class, 'store']);
        Route::post('list_draft_approval', [DraftController::class, 'listDraftApproval']);
        Route::post('attachment', [KbReportExport::class, 'attachmentReport']);
        Route::get('attachment', [KbReportExport::class, 'attachmentReport']);
        Route::post('attachmentSppd', [KbReportExport::class, 'sppdReport']);
    });

    Route::group(["prefix" => "dashboard"], function () {
        Route::post('ppjk', [DashboardController::class, 'ppjk']);
        Route::post('shortcut', [DashboardController::class, 'shortcut']);
    });

    Route::group(["prefix" => "master"], function () {
        Route::post('agent-type', [AgentController::class, 'agentType']);
        Route::post('currency-data', [CurrencyController::class, 'currencyData']);
        Route::post('country', [MasterCountryController::class, 'show']);
        Route::post('cargo-type', [CargoController::class, 'cargoType']);
        Route::get('cargo-local', [CargoController::class, 'getCargoLocal']);
        Route::get('cargo-local-index', [CargoController::class, 'getCargoLocal']);
        Route::post('cargo-type-local', [CargoController::class, 'cargoTypeLocal']);
        Route::post('display-form-user', [DisplayFormController::class, 'showForm']);
        Route::post('display-form-user-show', [DisplayFormController::class, 'userDisplayForm']);
        Route::post('/mtype/{no}', [TransTypeController::class, 'getTransType']);
        Route::post('roles', [UserRoleController::class, 'roles']);
        Route::post('user/mastertenant/{id}', [UserController::class, 'masterTenant']);
        Route::post('user/session/{id}', [UserController::class, 'userSessionLogs']);
        Route::post('tenant-bill', [PriceListTenantControler::class, 'tenantBill']);
        Route::post('user/tenant/{id}', [UserController::class, 'getTenants']);
        Route::post('user-role', [UserRoleController::class, 'userRole']);
        Route::post('user-permission', [UserController::class, 'getPermission']);
        Route::post('users/permission', [UserController::class, 'storeUserPermission']);
        Route::post('users/permission-index', [UserController::class, 'userPermission']);
        Route::post('/user-login-log/{id}', [UserController::class, "loginLog"]);
        Route::post('tipebcgroup', [TipeBcController::class, 'tipeBcGroup']);

        Route::post('display-form-user', [DisplayFormController::class, 'storeForm']);
        Route::post('display-form-user-remove', [DisplayFormController::class, 'deleteForm']);
        Route::post('roles/{id}', [UserRoleController::class, 'postRole']);
        Route::post('roles/delete/{id}', [UserRoleController::class, 'deleteRole']);
        Route::post('user/logout/{id}', [UserController::class, 'logoutUser']);
        Route::post('user/tenant-post/{id}', [UserController::class, 'postTenant']);
        Route::post('user/tenant-remove/{id}', [UserController::class, 'deleteTenant']);
        Route::post('user-password/{id}', [UserController::class, "updatePassword"]);
        Route::post('user-profile/{id}', [UserController::class, "updateNameEmail"]);
        Route::post('tenant-sign-user', [TenantController::class, "signTenantToUser"]);
        Route::post('user-copy', [UserController::class, "copyUserData"]);

        Route::post('permission-role', [MasterRolesController::class, 'permissionRole']);
        Route::post('permission-role-store', [MasterRolesController::class, 'storePermissionRole']);

        Route::get('agent-index', [AgentController::class, 'index']);
        Route::post('agent/{id}', [AgentController::class, 'show']);

        Route::post('exchange-rate-index', [ExchangeRateController::class, 'index']);
        Route::post('exchange-rate/{id}', [ExchangeRateController::class, 'show']);

        Route::get('bp-index', [BusinessPartnerController::class, 'index']);
        Route::post('bp/{id}', [BusinessPartnerController::class, 'show']);

        Route::get('billing-item-index', [BillingItemController::class, 'index']);
        Route::post('billing/{id}', [BillingItemController::class, 'show']);

        Route::post('cargo-index', [CargoController::class, 'index']);
        Route::post('cargo/{id}', [CargoController::class, 'show']);
        Route::get('cargo-select', [CargoController::class, 'getSelectCargo']);

        Route::get('classification-index', [ClassificationController::class, 'index']);
        Route::post('classification/{id}', [ClassificationController::class, 'show']);

        Route::get('cat-classification-index', [MasterCategoryClassification::class, 'index']);
        Route::post('cat-classification/{id}', [MasterCategoryClassification::class, 'show']);

        Route::get('category-item-index', [CategoryItemController::class, 'index']);
        Route::post('category-item/{id}', [CategoryItemController::class, 'show']);

        Route::get('currency-index', [CurrencyController::class, 'index']);
        Route::post('currency/{id}', [CurrencyController::class, 'show']);

        Route::get('display-form-index', [DisplayFormController::class, 'index']);
        Route::post('display-form/{id}', [DisplayFormController::class, 'show']);

        Route::get('destination-port-index', [DestinationPortController::class, 'index']);
        Route::post('destination-port/{id}', [DestinationPortController::class, 'show']);

        Route::post('mt-index', [MappingTableController::class, 'index']);
        Route::post('mt/{id}', [MappingTableController::class, 'show']);

        Route::post('local-item-index', [LocalItemController::class, 'index']);
        Route::post('local-item/{id}', [LocalItemController::class, 'show']);

        Route::post('jetty-index', [JettyController::class, 'index']);
        Route::post('jetty/{id}', [JettyController::class, 'show']);

        Route::post('ppjk-index', [MasterPPJKController::class, 'index']);
        Route::post('ppjk/{id}', [MasterPPJKController::class, 'show']);

        Route::post('port-of-loading-index', [PortOfLoadingController::class, 'index']);
        Route::post('port-of-loading/{id}', [PortOfLoadingController::class, 'show']);

        Route::post('price-list-ps-index', [PriceListPortSeviceControler::class, 'index']);
        Route::post('price-list-ps/{id}', [PriceListPortSeviceControler::class, 'show']);

        Route::post('price-list-at-index', [PriceListAssisTugController::class, 'index']);

        Route::post('price-list-sl-index', [PriceListServiceLoadingControler::class, 'index']);
        Route::post('price-list-sl/{id}', [PriceListServiceLoadingControler::class, 'show']);

        Route::post('price-list-tenant-index', [PriceListTenantControler::class, 'index']);
        Route::post('price-list-tenant/{id}', [PriceListTenantControler::class, 'show']);

        Route::post('remark-index', [RemarkController::class, 'index']);
        Route::post('remark/{id}', [RemarkController::class, 'show']);

        Route::post('role-index', [RolesController::class, 'index']);
        Route::post('role/{id}', [RolesController::class, 'show']);

        Route::post('tenant-index', [TenantController::class, 'index']);
        Route::post('tenant/{id}', [TenantController::class, 'show']);

        Route::post('signature-index', [SignatureController::class, 'index']);
        Route::post('signature/{id}', [SignatureController::class, 'show']);

        Route::post('tipebc-index', [TipeBcController::class, 'index']);
        Route::post('tipebc/{id}', [TipeBcController::class, 'show']);

        Route::post('tongkang-index', [TongkangController::class, 'index']);
        Route::post('tongkang/{id}', [TongkangController::class, 'show']);

        Route::post('transtype-index', [TransTypeController::class, 'index']);
        Route::post('transtype/{id}', [TransTypeController::class, 'show']);

        Route::post('tugboat-index', [TugboatController::class, 'index']);
        Route::post('tugboat/{id}', [TugboatController::class, 'show']);

        Route::post('user-email-index', [UserEmailController::class, 'index']);
        Route::post('user-email/{id}', [UserEmailController::class, 'show']);

        Route::post('user-index', [UserController::class, 'index']);
        Route::post('user/{id}', [UserController::class, 'show']);

        Route::post('signer-index', [MasterSignerController::class, 'index']);
        Route::post('signer/{id}', [MasterSignerController::class, 'show']);

        Route::post('permissions-index', [MasterPermissionController::class, 'index']);
        Route::post('permissions/{id}', [MasterPermissionController::class, 'show']);

        Route::post('roles-index', [MasterRolesController::class, 'index']);

        Route::apiResources([
            'roles' => MasterRolesController::class,
            'surveyor' => SurveyorController::class,
            'trading' => MasterTradingController::class,
            'agent' => AgentController::class,
            'exchange-rate' => ExchangeRateController::class,
            'bp' => BusinessPartnerController::class,
            'billing-item' => BillingItemController::class,
            'cargo' => CargoController::class,
            'classification' => ClassificationController::class,
            'cat-classification' => MasterCategoryClassification::class,
            'category-item' => CategoryItemController::class,
            'currency' => CurrencyController::class,
            'display-form' => DisplayFormController::class,
            'destination-port' => DestinationPortController::class,
            'mt' => MappingTableController::class,
            'local-item' => LocalItemController::class,
            'jetty' => JettyController::class,
            'ppjk' => MasterPPJKController::class,
            'port-of-loading' => PortOfLoadingController::class,
            'price-list-ps' => PriceListPortSeviceControler::class,
            'price-list-at' => PriceListAssisTugController::class,
            'tugboat' => TugboatController::class,
            'price-list-sl' => PriceListServiceLoadingControler::class,
            'price-list-tenant' => PriceListTenantControler::class,
            'permissions' => MasterPermissionController::class,
            'signer' => MasterSignerController::class,
            'user' => UserController::class,
            'user-email' => UserEmailController::class,
            'transtype' => TransTypeController::class,
            'tongkang' => TongkangController::class,
            'tipebc' => TipeBcController::class,
            'tenant-letter' => TenantLetterController::class,
            'signature' => SignatureController::class,
            'tenant' => TenantController::class,
            'role' => RolesController::class,
            'remark' => RemarkController::class,
        ]);
    });


    Route::group(["prefix" => "email"], function () {
        Route::post('send', [SendEmailController::class, 'sendEmail']);
        Route::post('re-send-change', [ImportController::class, 'reSendEmailChange']);
        Route::post('export-mail', [SendEmailController::class, 'sendEmailExport']);
    });


    Route::group(["prefix" => "employee"], function () {
        Route::post('list', [EmployeeController::class, 'index']);
        Route::post('department', [EmployeeController::class, 'department']);
        Route::post('get-department', [DepartmentController::class, 'index']);
        Route::post('show', [EmployeeController::class, 'show']);
        Route::post('workshift', [WorkShiftController::class, 'index']);
        Route::post('workshift-export', [WorkShiftController::class, 'export']);
        Route::post('workshift/{id}', [WorkShiftController::class, 'show']);
        Route::post('workshift-store', [WorkShiftController::class, 'store']);

        Route::post('reguler', [EmployeeController::class, 'showEmployeeWorkShift']);
        Route::post('nik', [EmployeeController::class, 'showOnlyNik']);
        Route::post('by-nik/{nik}', [EmployeeController::class, 'employeeByNik']);

        Route::put('update-reguler', [EmployeeController::class, 'updateEmployeeWorkShift']);
        Route::put('department', [DepartmentController::class, 'update']);
        Route::put('update', [EmployeeController::class, 'update']);
        Route::put('workshift/{id}', [WorkShiftController::class, 'update']);

        Route::delete('remove-reguler', [EmployeeController::class, 'destroyReguler']);
        Route::delete('delete-employee', [EmployeeController::class, 'destroy']);
        Route::delete('delete-department', [DepartmentController::class, 'destroy']);

        Route::post('list-department/{user_id}', [DepartmentController::class, 'masterDepartment']);
        Route::post('user-department/{user_id}', [DepartmentController::class, 'userDepartments']);
        Route::post('store-user-department/{user_id}', [DepartmentController::class, 'postDepartment']);
        Route::post('delete-user-department/{user_id}', [DepartmentController::class, 'deleteUserDepartment']);
    });


    Route::post('catalog/tenant', [CatalogController::class, 'callTenant']);
    Route::post('catalog/getBC', [CatalogController::class, 'getBC']);
    Route::post('catalog/LoadItem/{ItemId}', [CatalogController::class, 'loadItem']);
    Route::post('catalog/searchSAPDesc/{ItemCode}', [CatalogController::class, 'searchSAPDesc']);
    Route::post('catalogDetail/{id}', [CatalogDetailController::class, 'detail']);
    Route::post('catalog/image/{ItemId}', [CatalogImgController::class, 'show']);
    Route::post('catalog/ItemCode', [CatalogImgController::class, 'showItemCode']);

    Route::post('catalogDetail', [CatalogDetailController::class, 'save']);
    Route::post('catalog/image', [CatalogImgController::class, 'store']);
    Route::post('catalogExport', [CatalogController::class, 'catalotExcelExport']);

    Route::delete('catalog/image/{ItemId}', [CatalogImgController::class, 'destroy']);
    Route::delete('catalogDetail/{id}', [CatalogDetailController::class, 'delete']);

    Route::post('catalog-index', [CatalogController::class, 'index']);
    Route::apiResource('catalog', CatalogController::class);
});
