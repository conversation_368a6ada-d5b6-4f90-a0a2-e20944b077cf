<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('T_MDOC_Container', function (Blueprint $table) {
            $table->bigIncrements('DocEntry');
            $table->bigInteger('InvId');
            $table->string('ContainerNo', 200);
            $table->string('ContainerSize', 100);
            $table->string('Remark', 200)->nullable();
            $table->bigInteger('CreatedBy');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('T_MDOC_Container');
    }
};
